# LORE-TSR TableLabelMe数据格式适配详细设计文档（LLD）

## 项目结构与总体设计

### 核心设计哲学
1. **完全兼容原则**：对现有COCO格式数据处理流程零侵入
2. **增量集成策略**：通过继承机制扩展新格式支持
3. **格式转换透明化**：在数据加载层完成格式转换，上层业务逻辑无感知
4. **多源统一管理**：支持多个TableLabelMe数据目录的统一扫描和加载

## 目录结构树 (Directory Tree)

```
LORE-TSR/src/
├── lib/
│   ├── configs/
│   │   └── dataset_paths.py          # 🆕 预定义路径配置
│   ├── datasets/
│   │   ├── dataset_factory.py        # 🔄 添加tableme映射
│   │   └── dataset/
│   │       ├── table.py              # 📋 基类（无修改）
│   │       ├── table_mid.py          # 📋 现有类（无修改）
│   │       └── table_tableme.py      # 🆕 TableLabelMe数据集类
│   └── opts.py                       # 🔄 添加新参数
```

## 整体逻辑和交互时序图

```mermaid
sequenceDiagram
    participant Main as main.py
    participant Opts as opts.py
    participant Factory as dataset_factory.py
    participant Config as dataset_paths.py
    participant TableMe as table_tableme.py
    participant Converter as format_converter

    Main->>Opts: 解析--dataset tableme参数
    Opts-->>Main: 返回配置对象opt

    Main->>Factory: get_dataset('tableme', 'ctdet_mid')
    Factory->>TableMe: 创建TableMe实例
    
    TableMe->>Config: 加载预定义路径配置
    Config-->>TableMe: 返回train/val数据根目录
    
    TableMe->>TableMe: _scan_multi_source_data()
    Note over TableMe: 根据split扫描对应数据目录
    
    loop 数据加载循环
        Main->>TableMe: __getitem__(index)
        TableMe->>TableMe: safe_load_annotation()
        TableMe->>Converter: convert_tablelabelme_to_coco()
        Converter-->>TableMe: COCO格式数据
        TableMe-->>Main: 标准化数据样本
    end
```

## 数据实体结构深化

```mermaid
erDiagram
    CONFIG {
        string config_name
        dict train_data_roots
        dict val_data_roots
        dict scan_options
    }
    
    TABLEME_ANNOTATION {
        string image_path
        list cells
        dict metadata
        string quality
    }
    
    CELL_DATA {
        dict lloc
        dict bbox
        string text
        string quality
    }
    
    COCO_ANNOTATION {
        list logic_axis
        list segmentation
        list bbox
        float area
        int category_id
    }
    
    SCAN_RESULT {
        list valid_pairs
        dict statistics
        list error_logs
    }

    CONFIG ||--o{ TABLEME_ANNOTATION : "配置加载"
    TABLEME_ANNOTATION ||--o{ CELL_DATA : "包含单元格"
    CELL_DATA ||--|| COCO_ANNOTATION : "格式转换"
    CONFIG ||--|| SCAN_RESULT : "扫描配置"
```

## 配置项

### 环境变量
- `TABLEME_DATA_CONFIG`: 指定使用的数据配置名称（默认：'tableme_full'）

### 命令行参数
- `--dataset='tableme'`: 指定使用TableLabelMe数据集
- `--dataset_name='TableLabelMe'`: 数据集显示名称
- `--data_config='tableme_full'`: 预定义配置名称

## 涉及到的文件详解 (File-by-File Breakdown)

### src/lib/configs/dataset_paths.py
a. **文件用途说明**: 预定义TableLabelMe数据集路径配置，支持train/val数据集分离配置

b. **文件内类图**: 无类定义，纯配置文件

c. **配置结构详解**:
```python
DATASET_CONFIGS = {
    'tableme_full': {
        'train': [
            '/path/to/tableme/train/part_0001',
            '/path/to/tableme/train/part_0002'
        ],
        'val': [
            '/path/to/tableme/val/part_0001',
            '/path/to/tableme/val/part_0002'
        ]
    },
    'tableme_subset': {
        'train': ['/path/to/tableme/subset/train'],
        'val': ['/path/to/tableme/subset/val']
    }
}
```

#### 配置加载函数详解
- **用途**: 根据配置名称和数据集分割类型加载对应的数据路径配置
- **输入参数**: 
  - `config_name: str` - 配置名称
  - `split: str` - 数据集分割类型（train/val/test）
- **输出数据结构**: `List[str]` - 数据根目录路径列表
- **实现流程**:
```mermaid
flowchart TD
    A[接收config_name和split] --> B{配置是否存在?}
    B -->|是| C{split是否存在?}
    B -->|否| D[抛出KeyError异常]
    C -->|是| E[返回对应split的路径列表]
    C -->|否| F[抛出ValueError异常]
```

### src/lib/datasets/dataset/table_tableme.py
a. **文件用途说明**: TableLabelMe格式数据集类，继承自Table基类，实现格式转换和多源数据加载

b. **文件内类图**:
```mermaid
classDiagram
    class Table {
        +__init__(opt, split)
        +__len__()
        +__getitem__(index)
        #_get_default_calib()
    }
    
    class TableMe {
        -data_config: dict
        -valid_pairs: list
        -quality_stats: dict
        -split: str
        +__init__(opt, split)
        +__len__()
        +__getitem__(index)
        -_load_data_config()
        -_scan_multi_source_data()
        -_scan_part_directory(root_dir)
        -_find_annotation_file(image_path)
        -_filter_by_quality(annotation)
        +safe_load_annotation(ann_path)
        +convert_tablelabelme_to_coco(annotation)
        +convert_logic_axis(lloc)
        +convert_bbox_format(bbox)
        +calculate_polygon_area(points)
    }
    
    Table <|-- TableMe
```

#### 核心方法详解

**__init__方法**
- **用途**: 初始化TableMe数据集，加载配置并扫描对应split的数据
- **输入参数**: 
  - `opt`: 配置对象，包含data_config等参数
  - `split`: 数据集分割类型（train/val/test）
- **输出数据结构**: 无返回值，初始化实例
- **实现流程**:
```mermaid
sequenceDiagram
    participant Init as __init__
    participant Config as _load_data_config
    participant Scan as _scan_multi_source_data
    participant Filter as _filter_by_quality

    Init->>Config: 加载数据配置(config_name, split)
    Config-->>Init: 返回对应split的路径列表
    Init->>Scan: 扫描多源数据
    Scan->>Filter: 质量过滤
    Filter-->>Scan: 有效数据对
    Scan-->>Init: 扫描结果统计
```

**_scan_multi_source_data方法**
- **用途**: 扫描当前split对应的所有数据根目录，找到有效的图像-标注文件对
- **输入参数**: 无
- **输出数据结构**: `List[Tuple[str, str]]` - (图像路径, 标注路径)对列表
- **实现流程**:
```mermaid
flowchart TD
    A[开始扫描] --> B[遍历当前split的data_roots]
    B --> C[调用_scan_part_directory]
    C --> D[查找图像文件]
    D --> E[调用_find_annotation_file]
    E --> F{找到标注文件?}
    F -->|是| G[添加到valid_pairs]
    F -->|否| H[记录错误日志]
    G --> I{还有目录?}
    H --> I
    I -->|是| B
    I -->|否| J[返回扫描结果]
```

**convert_tablelabelme_to_coco方法**
- **用途**: 将TableLabelMe格式标注转换为LORE-TSR内部COCO格式
- **输入参数**: `annotation: dict` - TableLabelMe格式标注
- **输出数据结构**: `dict` - COCO格式标注
- **实现流程**:
```mermaid
flowchart TD
    A[接收TableLabelMe标注] --> B[提取cells列表]
    B --> C[遍历每个cell]
    C --> D[convert_logic_axis转换逻辑坐标]
    D --> E[convert_bbox_format转换边界框]
    E --> F[calculate_polygon_area计算面积]
    F --> G[构建COCO格式annotation]
    G --> H{还有cell?}
    H -->|是| C
    H -->|否| I[返回COCO格式数据]
```

**convert_logic_axis方法**
- **用途**: 将TableLabelMe的lloc格式转换为LORE-TSR的logic_axis格式
- **输入参数**: `lloc: dict` - {"start_row": int, "end_row": int, "start_col": int, "end_col": int}
- **输出数据结构**: `List[List[int]]` - [[start_row, end_row, start_col, end_col]]
- **实现流程**:
```mermaid
flowchart TD
    A[接收lloc字典] --> B[提取start_row, end_row, start_col, end_col]
    B --> C[构建logic_axis格式]
    C --> D[返回转换结果]
```

**convert_bbox_format方法**
- **用途**: 将TableLabelMe的bbox格式转换为COCO的segmentation、bbox、area格式
- **输入参数**: `bbox: dict` - {"p1": [x1, y1], "p2": [x2, y2], "p3": [x3, y3], "p4": [x4, y4]}
- **输出数据结构**: `dict` - {"segmentation": [[...]], "bbox": [x, y, w, h], "area": float}
- **实现流程**:
```mermaid
flowchart TD
    A[接收bbox字典] --> B[提取四个顶点坐标]
    B --> C[构建segmentation格式]
    C --> D[计算最小外接矩形bbox]
    D --> E[使用鞋带公式计算多边形面积]
    E --> F[返回转换结果]
```

**safe_load_annotation方法**
- **用途**: 安全加载TableLabelMe标注文件，处理异常情况
- **输入参数**: `ann_path: str` - 标注文件路径
- **输出数据结构**: `dict` - 标注数据，失败时返回None
- **实现流程**:
```mermaid
flowchart TD
    A[接收标注文件路径] --> B[尝试读取JSON文件]
    B --> C{读取成功?}
    C -->|是| D[验证必要字段]
    C -->|否| E[记录错误日志]
    D --> F{字段完整?}
    F -->|是| G[返回标注数据]
    F -->|否| H[记录警告日志]
    E --> I[返回None]
    H --> I
```

### src/lib/opts.py (修改部分)
a. **文件用途说明**: 在现有配置解析基础上添加TableLabelMe相关参数

#### 新增参数详解
- **--dataset='tableme'**: 数据集类型选择
- **--dataset_name='TableLabelMe'**: 数据集显示名称  
- **--data_config='tableme_full'**: 预定义配置选择

#### 修改内容详解
```python
# 在__init__方法中添加新参数
self.parser.add_argument('--data_config', default='tableme_full',
                        help='预定义数据路径配置名称')

# 在parse方法中处理tableme数据集的特殊逻辑
if opt.dataset == 'tableme':
    # 将data_config传递给数据集类
    opt.tableme_config = opt.data_config
```

### src/lib/datasets/dataset_factory.py (修改部分)
a. **文件用途说明**: 在现有数据集工厂中添加TableMe类映射

#### 修改内容详解
```python
# 添加导入
from .dataset.table_tableme import TableMe

# 修改dataset_factory字典
dataset_factory = {
    'table': Table,
    'table_mid': Table_mid,
    'table_small': Table_small,
    'tableme': TableMe,  # 新增映射
}
```

## 迭代演进依据

### 架构扩展性设计
1. **配置系统可扩展**: `dataset_paths.py`支持添加新的数据源配置
2. **格式转换器可插拔**: 转换逻辑独立，便于支持其他格式
3. **质量过滤可配置**: 支持不同质量标准的灵活配置
4. **多源扫描可优化**: 预留缓存和并行扫描的扩展空间

### 后期迭代占位实现
```python
# 迭代2: 高级质量过滤 (占位实现)
def advanced_quality_filter(self, annotation):
    """高级质量过滤功能 - 迭代2实现"""
    return True  # 固定返回值占位

# 迭代3: 数据缓存优化 (占位实现)  
def enable_data_cache(self):
    """数据缓存优化 - 迭代3实现"""
    pass  # 空实现占位

# 迭代4: 并行数据加载 (占位实现)
def parallel_data_loading(self):
    """并行数据加载 - 迭代4实现"""
    pass  # 空实现占位
```

## 如何迁移现有COCO格式功能

### 兼容性保证
1. **零侵入原则**: 现有`table.py`、`table_mid.py`等文件完全不修改
2. **接口一致性**: `TableMe`类实现与现有类相同的接口
3. **数据格式统一**: 转换后的数据格式与现有COCO格式完全一致

### 代码文件对应关系
| 现有文件 | 新增文件 | 关系说明 |
|---------|---------|----------|
| `table.py` | `table_tableme.py` | 继承关系，复用基础功能 |
| `opts.py` | `opts.py` | 增量修改，添加新参数 |
| `dataset_factory.py` | `dataset_factory.py` | 增量修改，添加新映射 |
| 无 | `dataset_paths.py` | 全新配置文件 |

### 迁移验证策略
1. **功能验证**: 确保现有训练脚本正常运行
2. **性能验证**: 数据加载性能在可接受范围内
3. **精度验证**: 转换后数据的训练效果与原始数据一致



