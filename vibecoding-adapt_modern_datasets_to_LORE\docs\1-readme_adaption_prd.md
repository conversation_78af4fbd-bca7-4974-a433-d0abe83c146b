# LORE-TSR TableLabelMe数据格式适配需求文档（PRD）

## 📋 项目概述

### 需求背景
LORE-TSR表格结构识别项目当前仅支持集中式COCO格式数据集，需要扩展支持分布式TableLabelMe格式数据集，以实现多源数据集的统一训练和测试。

### 核心目标
在保持对现有WTW-COCO格式完全兼容的前提下，增量式地集成TableLabelMe格式支持，实现两种数据格式的并行使用。

### 设计原则
- **完全兼容**：对现有COCO格式数据处理流程零侵入
- **增量集成**：通过并行机制支持新格式，不修改现有代码
- **格式转换**：实现TableLabelMe到LORE-TSR内部格式的无损转换
- **多源支持**：支持多个数据目录的统一加载和处理

## 🔄 数据格式对比分析

### 现有格式：WTW-COCO
**目录结构**：
```
data_dir/
├── images/           # 共享图像目录
└── json/
    ├── train.json    # 训练集标注
    ├── test.json     # 测试集标注
    └── val.json      # 验证集标注
```

**标注格式关键字段**：
```json
{
  "logic_axis": [[row_start, row_end, col_start, col_end]],
  "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
  "bbox": [x, y, width, height],
  "area": 13776.0
}
```

### 目标格式：TableLabelMe
**目录结构**：
```
data_root/
├── part_0001/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
├── part_0002/
│   ├── xxx.jpg/png
│   ├── xxx.json 或 xxx_table_annotation.json
│   └── ...
└── ...
```

**标注格式关键字段**：
```json
{
  "lloc": {
    "start_row": 0, "end_row": 0,
    "start_col": 0, "end_col": 0
  },
  "bbox": {
    "p1": [x1, y1], "p2": [x2, y2],
    "p3": [x3, y3], "p4": [x4, y4]
  },
  "quality": "合格"
}
```

## 🛠️ 技术实现方案

### 1. 配置系统扩展

#### 1.1 预定义路径配置文件
**新建文件**：`src/lib/configs/dataset_paths.py`

```python
DATASET_PATH_CONFIGS = {
    'tableme_full': {
        'train': [
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/train',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/train',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/train'
        ],
        'val': [
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/val',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/val',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/val',
            '/aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/val'
        ]
    },
    'tableme_subset': {
        'train': ['/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train'],
        'val': ['/aipdf-mlp/shared/tsr_training/wired_table/release/WTW/val']
    }
}
```

#### 1.2 命令行参数扩展
**修改文件**：`src/lib/opts.py`

**新增参数**：
- `--dataset='tableme'`：指定使用TableLabelMe格式
- `--dataset_name='TableLabelMe'`：数据集名称标识
- `--data_config='tableme_full'`：选择预定义的路径配置

**使用方式**：
```bash
# 新格式使用方式
python main.py ctdet --dataset tableme --dataset_name TableLabelMe --data_config tableme_full

# 现有格式保持不变
python main.py ctdet --dataset table --dataset_name WTW --data_dir /path/to/coco/data
```

### 2. 数据集实现架构

#### 2.1 新建数据集类
**新建文件**：`src/lib/datasets/dataset/table_tableme.py`

**类设计**：
```python
class TableMe(Table):
    """TableLabelMe格式数据集类"""
    
    def __init__(self, opt, split):
        # 继承基础配置
        super(TableMe, self).__init__(opt, split)
        
        # 重写数据加载逻辑
        self.data_paths = self._load_data_config(opt.data_config)
        self.samples = self._scan_multi_source_data()
        self.filtered_samples = self._filter_by_quality()
        
    def _load_data_config(self, config_name):
        """加载预定义路径配置"""
        
    def _scan_multi_source_data(self):
        """扫描多源数据目录"""
        
    def _filter_by_quality(self):
        """过滤质量合格的样本"""
        
    def __getitem__(self, index):
        """重写数据获取逻辑，实现格式转换"""
```

#### 2.2 支持的任务类型
- `ctdet`：标准表格检测任务（1024×1024分辨率）
- `ctdet_mid`：中等分辨率任务（768×768分辨率）
- `ctdet_small`：小分辨率任务（512×512分辨率）

### 3. 格式转换核心逻辑

#### 3.1 逻辑位置转换
```python
def convert_logic_axis(lloc_dict):
    """
    TableLabelMe lloc -> COCO logic_axis
    
    Input: {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
    Output: [[0.0, 0.0, 0.0, 0.0]]
    """
    return [[
        float(lloc_dict['start_row']),
        float(lloc_dict['end_row']),
        float(lloc_dict['start_col']),
        float(lloc_dict['end_col'])
    ]]
```

#### 3.2 几何信息转换
```python
def convert_bbox_format(bbox_dict):
    """
    TableLabelMe bbox -> COCO segmentation + bbox + area
    
    Input: {"p1": [x1,y1], "p2": [x2,y2], "p3": [x3,y3], "p4": [x4,y4]}
    Output: {
        "segmentation": [[x1, y1, x2, y2, x3, y3, x4, y4]],
        "bbox": [x_min, y_min, width, height],
        "area": calculated_area
    }
    """
    # 提取四个顶点坐标（已保证顺时针）
    points = [bbox_dict['p1'], bbox_dict['p2'], bbox_dict['p3'], bbox_dict['p4']]
    
    # 构建segmentation
    segmentation = []
    for point in points:
        segmentation.extend(point)
    
    # 计算标准bbox
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    bbox = [x_min, y_min, x_max - x_min, y_max - y_min]
    
    # 计算面积（使用鞋带公式）
    area = calculate_polygon_area(points)
    
    return {
        "segmentation": [segmentation],
        "bbox": bbox,
        "area": area
    }
```

#### 3.3 主转换函数
```python
def convert_tablelabelme_to_coco(tablelabelme_annotation):
    """
    完整的格式转换函数
    
    将TableLabelMe格式转换为LORE-TSR内部使用的COCO格式
    """
    coco_annotation = {}
    
    # 基础字段复制
    coco_annotation['id'] = tablelabelme_annotation.get('cell_ind', 0)
    coco_annotation['category_id'] = 1  # 统一为表格单元格类别
    coco_annotation['iscrowd'] = 0
    coco_annotation['ignore'] = 0
    
    # 逻辑位置转换
    coco_annotation['logic_axis'] = convert_logic_axis(
        tablelabelme_annotation['lloc']
    )
    
    # 几何信息转换
    geometry_info = convert_bbox_format(
        tablelabelme_annotation['bbox']
    )
    coco_annotation.update(geometry_info)
    
    return coco_annotation
```

### 4. 数据加载流程设计

#### 4.1 多源目录处理
```python
def _scan_multi_source_data(self):
    """
    扫描多个数据根目录，收集所有有效样本
    
    Returns:
        List[Dict]: 样本信息列表
    """
    all_samples = []
    
    for data_root in self.data_paths[self.split]:
        # 扫描part子目录
        part_dirs = [d for d in os.listdir(data_root) if d.startswith('part_')]
        
        for part_dir in sorted(part_dirs):
            part_path = os.path.join(data_root, part_dir)
            samples = self._scan_part_directory(part_path)
            all_samples.extend(samples)
    
    return all_samples

def _scan_part_directory(self, part_path):
    """扫描单个part目录"""
    samples = []
    
    # 获取所有图像文件
    image_files = glob.glob(os.path.join(part_path, '*.jpg')) + \
                  glob.glob(os.path.join(part_path, '*.png'))
    
    for image_path in image_files:
        # 查找对应的标注文件
        annotation_path = self._find_annotation_file(image_path)
        
        if annotation_path and os.path.exists(annotation_path):
            samples.append({
                'image_path': image_path,
                'annotation_path': annotation_path,
                'part_dir': part_path
            })
    
    return samples

def _find_annotation_file(self, image_path):
    """查找图像对应的标注文件"""
    base_name = os.path.splitext(image_path)[0]
    
    # 优先查找同名.json
    json_path = base_name + '.json'
    if os.path.exists(json_path):
        return json_path
    
    # 查找_table_annotation.json
    table_json_path = base_name + '_table_annotation.json'
    if os.path.exists(table_json_path):
        return table_json_path
    
    return None
```

#### 4.2 质量过滤机制
```python
def _filter_by_quality(self):
    """
    过滤质量合格的样本
    
    Returns:
        List[Dict]: 过滤后的样本列表
    """
    filtered_samples = []
    quality_stats = {'合格': 0, '不合格': 0, '其他': 0, '缺失': 0}
    
    for sample in self.samples:
        try:
            with open(sample['annotation_path'], 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)
            
            # 检查quality字段
            if 'cells' in annotation_data:
                # 检查所有单元格的质量
                all_qualified = True
                for cell in annotation_data['cells']:
                    quality = cell.get('quality', '缺失')
                    if quality != '合格':
                        all_qualified = False
                        break
                
                if all_qualified:
                    filtered_samples.append(sample)
                    quality_stats['合格'] += 1
                else:
                    quality_stats['不合格'] += 1
            else:
                quality_stats['缺失'] += 1
                
        except Exception as e:
            print(f"警告：无法解析标注文件 {sample['annotation_path']}: {e}")
            quality_stats['其他'] += 1
    
    # 输出统计信息
    total_samples = len(self.samples)
    filtered_count = len(filtered_samples)
    
    print(f"==> TableLabelMe数据集质量统计:")
    print(f"    总样本数: {total_samples}")
    print(f"    合格样本: {quality_stats['合格']} ({quality_stats['合格']/total_samples*100:.1f}%)")
    print(f"    不合格样本: {quality_stats['不合格']}")
    print(f"    质量字段缺失: {quality_stats['缺失']}")
    print(f"    解析错误: {quality_stats['其他']}")
    print(f"    最终使用样本: {filtered_count}")
    
    return filtered_samples
```

### 5. 集成点实现

#### 5.1 数据集工厂扩展
**修改文件**：`src/lib/datasets/dataset_factory.py`

```python
# 添加新的数据集映射
dataset_factory = {
    'table': Table,
    'tableme': TableMe,  # 新增
    # ... 其他现有映射
}

# 确保正确导入
from .dataset.table_tableme import TableMe
```

#### 5.2 配置系统集成
**修改文件**：`src/lib/opts.py`

```python
# 导入路径配置
from .configs.dataset_paths import DATASET_PATH_CONFIGS

class opts(object):
    def __init__(self):
        # ... 现有参数
        
        # 新增参数
        self.parser.add_argument('--data_config', default='tableme_full',
                                help='预定义数据路径配置名称')
        
    def parse(self, args=''):
        opt = self.parser.parse_args(args)
        
        # 处理数据路径配置
        if opt.dataset == 'tableme':
            if opt.data_config in DATASET_PATH_CONFIGS:
                opt.data_paths = DATASET_PATH_CONFIGS[opt.data_config]
            else:
                raise ValueError(f"未找到数据配置: {opt.data_config}")
        
        return opt
```

## 🔧 错误处理和容错机制

### 异常处理策略
- **文件解析错误**：捕获JSON解析异常，输出警告信息，跳过该样本
- **文件缺失**：检查图像和标注文件存在性，缺失时跳过并记录
- **格式错误**：验证必要字段存在性，格式错误时抛出明确异常
- **路径配置错误**：验证预定义配置存在性，不存在时抛出配置错误

### 错误信息输出
```python
def safe_load_annotation(annotation_path):
    """安全加载标注文件"""
    try:
        with open(annotation_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError(f"标注文件不存在: {annotation_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"标注文件格式错误 {annotation_path}: {e}")
    except Exception as e:
        raise RuntimeError(f"加载标注文件失败 {annotation_path}: {e}")
```

## 📊 验收标准

### 功能验收
1. **配置系统**：能够通过`--data_config`参数选择预定义路径配置
2. **数据加载**：能够正确扫描和加载多源TableLabelMe格式数据
3. **格式转换**：TableLabelMe格式能够无损转换为LORE-TSR内部格式
4. **质量过滤**：只加载`quality='合格'`的样本，并输出统计信息
5. **任务支持**：支持`ctdet`、`ctdet_mid`、`ctdet_small`三种任务类型
6. **兼容性**：现有COCO格式数据处理流程完全不受影响

### 性能验收
- **数据加载时间**：多源数据扫描时间在可接受范围内
- **内存使用**：数据加载不会造成内存泄漏
- **转换精度**：坐标转换精度损失控制在业界标准范围内

### 使用验收
```bash
# 验收测试命令 - 集成到完整训练脚本
python main.py ctdet_mid --dataset tableme --exp_id train_wireless --dataset_name TableLabelMe --data_config tableme_full --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5

# 预期输出包含：
# - 数据集质量统计信息
# - 成功加载的样本数量
# - 正常的训练流程启动
# - 模型训练过程正常进行

# 其他任务类型验收
python main.py ctdet --dataset tableme --dataset_name TableLabelMe --data_config tableme_subset --batch_size 8 --arch dla_34 --lr 1e-4 --num_epochs 100 --gpus 0

python main.py ctdet_small --dataset tableme --dataset_name TableLabelMe --data_config tableme_full --batch_size 16 --arch dla_34 --lr 1e-4 --num_epochs 150 --gpus 0
```

## 📁 文件清单

### 新建文件
- `src/lib/configs/dataset_paths.py` - 预定义路径配置
- `src/lib/datasets/dataset/table_tableme.py` - TableLabelMe数据集类

### 修改文件
- `src/lib/opts.py` - 添加新的命令行参数
- `src/lib/datasets/dataset_factory.py` - 添加数据集映射

### 依赖文件（无需修改）
- `src/lib/datasets/dataset/table_mid.py` - 继承基类
- 现有COCO格式相关文件保持不变

## 🚀 实施计划

### 阶段1：配置系统搭建（1天）
- 创建`dataset_paths.py`配置文件
- 扩展`opts.py`命令行参数
- 测试配置加载功能

### 阶段2：数据集类实现（2天）
- 创建`TableMe`类基础框架
- 实现多源数据扫描逻辑
- 实现质量过滤机制

### 阶段3：格式转换实现（2天）
- 实现逻辑位置转换函数
- 实现几何信息转换函数
- 实现主转换函数和精度验证

### 阶段4：集成测试（1天）
- 集成到数据集工厂
- 端到端功能测试
- 兼容性验证测试

总计：6个工作日